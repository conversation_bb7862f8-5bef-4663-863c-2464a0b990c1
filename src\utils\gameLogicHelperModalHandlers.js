import { CARD_TYPES, SPECIAL_CARDS_NAMES } from '@/utils/gameRules';
import { debugLog } from '@/utils/debugUtils';

export const handleSchlangenfrassModal = (gameState, payload) => {
  let newGameState = { ...gameState };
  if (payload.option === 'a') {
    if (payload.selectedCardId && payload.snakeIndex !== undefined) {
      const snakeToRemoveFrom = newGameState.playerSnakes[payload.snakeIndex];
      if (snakeToRemoveFrom) {
        const originalCard = snakeToRemoveFrom.find(c => c.id === payload.selectedCardId);
        newGameState.playerSnakes[payload.snakeIndex] = snakeToRemoveFrom.filter(c => c.id !== payload.selectedCardId);
        if (originalCard) {
            const cardToDiscard = { ...originalCard }; // Ensure we discard a copy
            newGameState.discardPile.push(cardToDiscard);
            newGameState.toastInfo = { title: "Schlangenfrass", description: `Karte ${cardToDiscard.name} aus eigener Schlange entfernt.` };
        } else {
             newGameState.toastInfo = { title: "Schlangenfrass", description: `Karte nicht gefunden.`, variant: "destructive" };
        }
      }
    }
  } else if (payload.option === 'b') {
     let blockadeRemoved = false;
     newGameState.playerSnakes = newGameState.playerSnakes.map(snake =>
        snake.filter(card => {
            if (card.name === 'Blockade' && !blockadeRemoved) {
                const cardToDiscard = { ...card };
                newGameState.discardPile.push(cardToDiscard);
                blockadeRemoved = true;
                return false;
            }
            return true;
        })
     );
     newGameState.toastInfo = blockadeRemoved
        ? { title: "Schlangenfrass", description: "Schlangenblockade entfernt." }
        : { title: "Schlangenfrass", description: "Keine Blockade zum Entfernen gefunden.", variant: "destructive" };
  } else if (payload.option === 'c') {
    let cardsRemovedCount = 0;
    for (let i = 0; i < newGameState.aiOpponentsData.length && cardsRemovedCount < 2; i++) {
      for (let j = 0; j < newGameState.aiOpponentsData[i].snakes.length && cardsRemovedCount < 2; j++) {
        const opponentSnake = newGameState.aiOpponentsData[i].snakes[j];
        if (opponentSnake.length > 0) {
          const removedCard = opponentSnake.pop();
          const cardToDiscard = { ...removedCard };
          if (cardToDiscard.name !== 'Blockade') {
            newGameState.discardPile.push(cardToDiscard);
            cardsRemovedCount++;
            newGameState.toastInfo = { title: "Schlangenfrass", description: `${cardsRemovedCount} Karte(n) von Gegnern entfernt.` };
          } else {
            opponentSnake.push(cardToDiscard);
          }
        }
      }
    }
    if (cardsRemovedCount === 0) {
        newGameState.toastInfo = { title: "Schlangenfrass", description: "Keine geeigneten Karten bei Gegnern gefunden.", variant: "destructive" };
    }
  }
  return newGameState;
};

export const handleFarbendiebModal = (gameState, payload) => {
  debugLog(`[FARBENDIEB-MODAL] Starting handleFarbendiebModal with payload:`, payload);
  console.log(`[FARBENDIEB-MODAL] Starting handleFarbendiebModal with payload:`, payload);
  let newGameState = { ...gameState };

  if (payload.step === 'card_selected') {
    // First step: Card selection - prepare for position selection
    debugLog(`[FARBENDIEB-MODAL] Step 1: Card selection`);
    const selectedCard = payload.selectedCard;

    if (!selectedCard) {
      debugLog(`[FARBENDIEB-MODAL] ERROR: No card selected`);
      newGameState.toastInfo = { title: "Fehler", description: "Keine Karte ausgewählt.", variant: "destructive" };
      return newGameState;
    }

    debugLog(`[FARBENDIEB-MODAL] Selected card:`, selectedCard);

    // Get the actual card object and source information
    let stolenCard = null;
    let sourceInfo = null;

    if (selectedCard.sourceType === 'player') {
      // Stealing from player's own snake
      debugLog(`[FARBENDIEB-MODAL] Stealing from player's own snake ${selectedCard.snakeIndex}, card ${selectedCard.cardIndex}`);
      const sourceSnake = newGameState.playerSnakes[selectedCard.snakeIndex];
      if (sourceSnake && sourceSnake[selectedCard.cardIndex]) {
        stolenCard = { ...sourceSnake[selectedCard.cardIndex] };
        sourceInfo = {
          type: 'player',
          snakeIndex: selectedCard.snakeIndex,
          cardIndex: selectedCard.cardIndex
        };
        debugLog(`[FARBENDIEB-MODAL] Found player card:`, stolenCard);
      }
    } else if (selectedCard.sourceType === 'opponent') {
      // Stealing from opponent snake
      debugLog(`[FARBENDIEB-MODAL] Stealing from opponent ${selectedCard.opponentIndex}, snake ${selectedCard.snakeIndex}, card ${selectedCard.cardIndex}`);
      const opponent = newGameState.aiOpponentsData[selectedCard.opponentIndex];
      if (opponent && opponent.snakes[selectedCard.snakeIndex] && opponent.snakes[selectedCard.snakeIndex][selectedCard.cardIndex]) {
        stolenCard = { ...opponent.snakes[selectedCard.snakeIndex][selectedCard.cardIndex] };
        sourceInfo = {
          type: 'opponent',
          opponentIndex: selectedCard.opponentIndex,
          snakeIndex: selectedCard.snakeIndex,
          cardIndex: selectedCard.cardIndex
        };
        debugLog(`[FARBENDIEB-MODAL] Found opponent card:`, stolenCard);
      }
    }

    if (!stolenCard) {
      debugLog(`[FARBENDIEB-MODAL] ERROR: Could not find selected card`);
      newGameState.toastInfo = { title: "Fehler", description: "Ausgewählte Karte nicht gefunden.", variant: "destructive" };
      return newGameState;
    }

    // Check for Farbenschutz protection when stealing from opponents
    if (sourceInfo.type === 'opponent') {
      const targetOpponent = newGameState.aiOpponentsData[sourceInfo.opponentIndex];
      if (targetOpponent && targetOpponent.protected) {
        debugLog(`[FARBENDIEB-MODAL] Theft blocked by Farbenschutz`);
        newGameState.toastInfo = { title: "Farbendieb abgewehrt!", description: `Gegner ${sourceInfo.opponentIndex + 1} ist durch Farbenschutz geschützt.` };
        return newGameState;
      }
    }

    // Proceed to position selection
    debugLog(`[FARBENDIEB-MODAL] Proceeding to position selection`);
    console.log(`[FARBENDIEB-MODAL] Proceeding to position selection`);
    newGameState.modalRequired = {
      type: 'FARBENDIEB_CHOOSE_POSITION',
      cardName: 'Farbendieb',
      step: 'choose_position',
      stolenCard: stolenCard,
      sourceInfo: sourceInfo
    };

    debugLog(`[FARBENDIEB-MODAL] Set modalRequired:`, newGameState.modalRequired);
    console.log(`[FARBENDIEB-MODAL] Set modalRequired:`, newGameState.modalRequired);
    return newGameState;

  } else if (payload.step === 'position_selected') {
    // Second step: Position selection - complete the theft/move
    debugLog(`[FARBENDIEB-MODAL] Step 2: Position selection`);
    console.log(`[FARBENDIEB-MODAL] Step 2: Position selection`);
    const { stolenCard, sourceInfo, targetSnakeIndex, targetPosition } = payload;

    debugLog(`[FARBENDIEB-MODAL] Position payload:`, { stolenCard, sourceInfo, targetSnakeIndex, targetPosition });
    console.log(`[FARBENDIEB-MODAL] Position payload:`, { stolenCard, sourceInfo, targetSnakeIndex, targetPosition });

    if (!stolenCard || !sourceInfo || targetSnakeIndex === undefined || targetPosition === undefined) {
      debugLog(`[FARBENDIEB-MODAL] ERROR: Missing information for position selection`);
      newGameState.toastInfo = { title: "Fehler", description: "Unvollständige Informationen für Kartenverschiebung.", variant: "destructive" };
      return newGameState;
    }

    // Remove card from source
    debugLog(`[FARBENDIEB-MODAL] Removing card from source: ${sourceInfo.type}`);
    if (sourceInfo.type === 'player') {
      debugLog(`[FARBENDIEB-MODAL] Removing from player snake ${sourceInfo.snakeIndex}, card ${sourceInfo.cardIndex}`);
      newGameState.playerSnakes[sourceInfo.snakeIndex] = newGameState.playerSnakes[sourceInfo.snakeIndex].filter((_, idx) => idx !== sourceInfo.cardIndex);
      debugLog(`[FARBENDIEB-MODAL] Player snake after removal:`, newGameState.playerSnakes[sourceInfo.snakeIndex]);
    } else if (sourceInfo.type === 'opponent') {
      debugLog(`[FARBENDIEB-MODAL] Removing from opponent ${sourceInfo.opponentIndex}, snake ${sourceInfo.snakeIndex}, card ${sourceInfo.cardIndex}`);
      const opponent = newGameState.aiOpponentsData[sourceInfo.opponentIndex];
      if (opponent && opponent.snakes[sourceInfo.snakeIndex]) {
        opponent.snakes[sourceInfo.snakeIndex] = opponent.snakes[sourceInfo.snakeIndex].filter((_, idx) => idx !== sourceInfo.cardIndex);
        debugLog(`[FARBENDIEB-MODAL] Opponent snake after removal:`, opponent.snakes[sourceInfo.snakeIndex]);
      }
    }

    // Insert card at target position
    debugLog(`[FARBENDIEB-MODAL] Inserting card at snake ${targetSnakeIndex}, position ${targetPosition}`);
    if (!newGameState.playerSnakes[targetSnakeIndex]) {
      newGameState.playerSnakes[targetSnakeIndex] = [];
      debugLog(`[FARBENDIEB-MODAL] Created new snake at index ${targetSnakeIndex}`);
    }

    const targetSnake = [...newGameState.playerSnakes[targetSnakeIndex]];
    debugLog(`[FARBENDIEB-MODAL] Target snake before insertion:`, targetSnake);
    targetSnake.splice(targetPosition, 0, stolenCard);
    newGameState.playerSnakes[targetSnakeIndex] = targetSnake;
    debugLog(`[FARBENDIEB-MODAL] Target snake after insertion:`, newGameState.playerSnakes[targetSnakeIndex]);

    // Set appropriate toast message
    if (sourceInfo.type === 'player') {
      newGameState.toastInfo = {
        title: "Farbendieb!",
        description: `Karte ${stolenCard.name} in deiner Schlange verschoben.`
      };
    } else {
      newGameState.toastInfo = {
        title: "Farbendieb!",
        description: `Karte ${stolenCard.name} von Gegner ${sourceInfo.opponentIndex + 1} gestohlen und platziert.`
      };
    }

    // Clear modal
    newGameState.modalRequired = null;
    debugLog(`[FARBENDIEB-MODAL] Cleared modal`);

    // KRITISCH: Entferne Farbendieb-Karte aus Hand und füge zum Abwurfstapel hinzu
    // (Das passiert normalerweise in applyCardEffect, aber für Modals machen wir es hier)
    const farbendiebCard = newGameState.playerHand.find(c => c.name === 'Farbendieb');
    if (farbendiebCard) {
      debugLog(`[FARBENDIEB-MODAL] Found Farbendieb card in hand:`, farbendiebCard);
      newGameState.playerHand = newGameState.playerHand.filter(c => c.id !== farbendiebCard.id);
      newGameState.discardPile = [...newGameState.discardPile, farbendiebCard];
      newGameState.specialCardsPlayedThisTurn = (newGameState.specialCardsPlayedThisTurn || 0) + 1;
      debugLog(`[FARBENDIEB-MODAL] Removed Farbendieb card from hand and added to discard pile`);
      debugLog(`[FARBENDIEB-MODAL] New hand size:`, newGameState.playerHand.length);
      debugLog(`[FARBENDIEB-MODAL] New discard pile size:`, newGameState.discardPile.length);
    } else {
      debugLog(`[FARBENDIEB-MODAL] WARNING: Farbendieb card not found in hand`);
    }

    debugLog(`[FARBENDIEB-MODAL] Farbendieb completed successfully!`);
    return newGameState;
  }

  // Fallback for old format (backward compatibility)
  if (payload.opponentIndex !== undefined && payload.snakeIndex !== undefined && payload.cardIndex !== undefined) {
    const opponent = newGameState.aiOpponentsData[payload.opponentIndex];
    if (opponent && opponent.snakes[payload.snakeIndex]) {
      const stolenCardOriginal = opponent.snakes[payload.snakeIndex].splice(payload.cardIndex, 1)[0];
      if (stolenCardOriginal) {
        const stolenCard = { ...stolenCardOriginal };
        const activeSnakeIdx = newGameState.activeSnakeIndex || 0;
        if (!newGameState.playerSnakes[activeSnakeIdx]) newGameState.playerSnakes[activeSnakeIdx] = [];
        newGameState.playerSnakes[activeSnakeIdx].push(stolenCard);
        newGameState.toastInfo = { title: "Farbendieb!", description: `Karte ${stolenCard.name} von Gegner ${payload.opponentIndex + 1} gestohlen.` };
      }
    }
  }

  return newGameState;
};

export const handleSchlangengrubeModal = (gameState, payload) => {
  let newGameState = { ...gameState };
  if (payload.opponentIndex !== undefined) {
    newGameState.aiOpponentsData = newGameState.aiOpponentsData.map((opp, idx) =>
      idx === payload.opponentIndex ? { ...opp, isBlockedByGrube: true } : opp
    );
    newGameState.toastInfo = { title: "Schlangengrube!", description: `Gegner ${payload.opponentIndex + 1} kann nächste Runde keine Karten anlegen.` };
  }
  return newGameState;
};

export const handleFarbenfusionModal = (gameState, payload) => {
  let newGameState = { ...gameState };
  if (payload.snakeIndex !== undefined && payload.card1Index !== undefined && payload.card2Index !== undefined) {
    const snakeToFuseIn = newGameState.playerSnakes[payload.snakeIndex];
    const card1Original = snakeToFuseIn[payload.card1Index];
    const card2Original = snakeToFuseIn[payload.card2Index];

    if (card1Original && card2Original && card1Original.color === card2Original.color && Math.abs(payload.card1Index - payload.card2Index) === 1) {
      const card1 = { ...card1Original };
      const card2 = { ...card2Original };
      const fusedCard = {
        id: `fused-${card1.id}-${card2.id}-${Date.now()}`, // Add timestamp for more uniqueness
        name: `Fusion: ${card1.name}`,
        type: CARD_TYPES.SPECIAL,
        color: card1.color,
        points: card1.points + card2.points,
        isFusion: true,
        fusedCards: [card1, card2],
        description: `Fusioniert ${card1.name} & ${card2.name}`
      };
      const minIndex = Math.min(payload.card1Index, payload.card2Index);
      snakeToFuseIn.splice(minIndex, 2, fusedCard);
      newGameState.playerSnakes[payload.snakeIndex] = snakeToFuseIn;
      newGameState.toastInfo = { title: "Farbenfusion!", description: `${card1.name} und ${card2.name} wurden fusioniert.` };
    } else {
      newGameState.toastInfo = { title: "Fusion fehlgeschlagen", description: "Karten müssen gleiche Farbe haben und benachbart sein.", variant: "destructive" };
    }
  }
  return newGameState;
};

export const handleSchlangenhaeutungModal = (gameState, payload) => {
  let newGameState = { ...gameState };
  if (payload.snakeIndex !== undefined && payload.newOrderArrayOfCards) {
    const snakeToReorder = newGameState.playerSnakes[payload.snakeIndex];
    const prefix = snakeToReorder.slice(0, payload.startIndex);
    const suffix = snakeToReorder.slice(payload.endIndex + 1);

    const newOrderedCardsCopies = payload.newOrderArrayOfCards.map(c => ({...c}));

    newGameState.playerSnakes[payload.snakeIndex] = [...prefix, ...newOrderedCardsCopies, ...suffix];

    newGameState.lastMoltingResult = {
        snakeIndex: payload.snakeIndex,
        originalSectionIds: snakeToReorder.slice(payload.startIndex, payload.endIndex + 1).map(c => c.id),
        moltedSectionIds: newOrderedCardsCopies.map(c => c.id),
        formedNewGroups: []
    };
    newGameState.toastInfo = { title: "Schlangenhäutung!", description: "Ein Abschnitt deiner Schlange wurde neu geordnet." };
  }
  return newGameState;
};

export const handleSchlangenkorbModal = (gameState, payload) => {
  let newGameState = { ...gameState };
  const korbCardOriginal = newGameState.discardPile.find(c => c.name === SPECIAL_CARDS_NAMES.SNAKE_BASKET_OF_LUCK);
  if (korbCardOriginal) newGameState.discardPile = newGameState.discardPile.filter(c => c.id !== korbCardOriginal.id);

  if (payload.option === 'a') {
    const drawnOriginal = newGameState.deck.slice(0, 3);
    const drawn = drawnOriginal.map(c => ({...c}));
    newGameState.deck = newGameState.deck.slice(3);
    newGameState.playerHand.push(...drawn);
    newGameState.toastInfo = { title: "Schlangenkorb!", description: "3 Karten gezogen. Spiele bis zu 2 davon." };
    newGameState.schlangenkorbPlayState = { active: true, cardsToPlay: 2, playedCount: 0 };
    return { ...newGameState, cardPlayedFromModal: true };
  } else if (payload.option === 'b') {
    if (payload.handCardIds && payload.snakeCardIds && payload.snakeCardIds.length === payload.handCardIds.length) {
        const snakeToSwapIn = newGameState.playerSnakes[payload.snakeIndex || 0];
        let cardsFromHandOriginal = [];
        let cardsFromSnakeOriginal = [];

        newGameState.playerHand = newGameState.playerHand.filter(hc => {
            if (payload.handCardIds.includes(hc.id)) {
                cardsFromHandOriginal.push(hc); return false;
            } return true;
        });

        for (const scId of payload.snakeCardIds) {
            const cardIdx = snakeToSwapIn.findIndex(sc => sc.id === scId);
            if (cardIdx > -1) cardsFromSnakeOriginal.push(snakeToSwapIn.splice(cardIdx, 1)[0]);
        }

        const cardsFromHand = cardsFromHandOriginal.map(c => ({...c}));
        const cardsFromSnake = cardsFromSnakeOriginal.map(c => ({...c}));

        newGameState.playerHand.push(...cardsFromSnake);
        snakeToSwapIn.push(...cardsFromHand);
        newGameState.playerSnakes[payload.snakeIndex || 0] = snakeToSwapIn;
        newGameState.toastInfo = { title: "Schlangenkorb!", description: `${cardsFromHand.length} Karte(n) getauscht.` };
    }
  } else if (payload.option === 'c') {
    if (payload.target === 'own_blockade') {
        let blockadeRemoved = false;
        newGameState.playerSnakes = newGameState.playerSnakes.map(snake =>
            snake.filter(card => {
                if (card.name === 'Blockade' && !blockadeRemoved) {
                    const cardToDiscard = {...card};
                    newGameState.discardPile.push(cardToDiscard);
                    blockadeRemoved = true; return false;
                } return true;
            })
        );
        newGameState.toastInfo = { title: "Schlangenkorb!", description: blockadeRemoved ? "Eigene Blockade entfernt." : "Keine Blockade gefunden."};
    } else if (payload.target === 'opponent_special' && payload.opponentIndex !== undefined && payload.cardId) {
        const oppSnake = newGameState.aiOpponentsData[payload.opponentIndex]?.snakes[payload.snakeIndex || 0];
        if (oppSnake) {
            const cardIdx = oppSnake.findIndex(c => c.id === payload.cardId && c.type === CARD_TYPES.SPECIAL);
            if (cardIdx > -1) {
                const removedOriginal = oppSnake.splice(cardIdx, 1)[0];
                const removed = {...removedOriginal};
                newGameState.discardPile.push(removed);
                newGameState.toastInfo = { title: "Schlangenkorb!", description: `Sonderkarte ${removed.name} von Gegner entfernt.`};
            }
        }
    }
  } else if (payload.option === 'd') {
    newGameState.cardsPlayedThisTurn = Math.max(-2, newGameState.cardsPlayedThisTurn - 2);
    newGameState.toastInfo = { title: "Schlangenkorb!", description: "Du darfst 2 zusätzliche Aktionen ausführen." };
  }
  return { ...newGameState, cardPlayedFromModal: false };
};

export const handleComebackCardModal = (gameState, payload, playCardFn) => {
    let newGameState = { ...gameState };
    newGameState.auxCards.comebackCard.used = true;
    newGameState.comebackActionsRemaining = 2;
    newGameState.comebackActionsRemainingInitial = 2;
    newGameState.comebackActive = true;
    newGameState.toastInfo = { title: "Comeback-Karte aktiviert!", description: "Du hast 2 zusätzliche Aktionen."};

    if (payload.actions && payload.actions.length > 0) {
        for (const action of payload.actions) {
            if (action.type === 'play_color_card' && action.cardId) {
                const cardOriginal = newGameState.playerHand.find(c => c.id === action.cardId);
                if (cardOriginal) {
                    const card = {...cardOriginal};
                    playCardFn(card, 'Schlangenzone', 'end', true);
                }
            } else if (action.type === 'play_special_card' && action.cardId) {
                const cardOriginal = newGameState.playerHand.find(c => c.id === action.cardId);
                if (cardOriginal) {
                    const card = {...cardOriginal};
                    playCardFn(card, null, null, true);
                }
            } else if (action.type === 'discard_draw') {
                if (newGameState.playerHand.length >= 2) {
                    const toDiscardOriginal = newGameState.playerHand.slice(0, 2);
                    const toDiscard = toDiscardOriginal.map(c => ({...c}));
                    newGameState.playerHand = newGameState.playerHand.slice(2);
                    newGameState.discardPile.push(...toDiscard);
                    const drawnOriginal = newGameState.deck.slice(0, 3);
                    const drawn = drawnOriginal.map(c => ({...c}));
                    newGameState.deck = newGameState.deck.slice(3);
                    newGameState.playerHand.push(...drawn);
                    newGameState.toastInfo = { title: "Comeback", description: "2 Karten abgeworfen, 3 gezogen."};
                } else {
                    newGameState.toastInfo = { title: "Comeback", description: "Nicht genug Karten zum Abwerfen.", variant: "destructive"};
                }
            }
        }
    }
    return newGameState;
};

export const handleRiskRewardCardModal = (gameState, payload, playCardFn) => {
    let newGameState = { ...gameState };
    const riskCardIndex = newGameState.auxCards.riskRewardCards.findIndex(c => !c.used);
    if (riskCardIndex !== -1) {
        newGameState.auxCards.riskRewardCards[riskCardIndex].used = true;
    } else {
        newGameState.toastInfo = { title: "Fehler", description: "Keine Risiko-Belohnungs-Karte verfügbar.", variant: "destructive" };
        return newGameState;
    }

    if (payload.option === 'draw_3') {
        const drawnOriginal = newGameState.deck.slice(0, 3);
        const drawn = drawnOriginal.map(c => ({...c}));
        newGameState.deck = newGameState.deck.slice(3);
        newGameState.playerHand.push(...drawn);
        newGameState.toastInfo = { title: "Risiko-Belohnung", description: "3 Karten gezogen."};
        if (newGameState.playerHand.length > 5) {
             const toDiscardCount = newGameState.playerHand.length - 5;
             const cardsToDiscardOriginal = newGameState.playerHand.slice(0, toDiscardCount);
             const cardsToDiscard = cardsToDiscardOriginal.map(c => ({...c}));
             newGameState.playerHand = newGameState.playerHand.slice(toDiscardCount);
             newGameState.discardPile = [...newGameState.discardPile, ...cardsToDiscard];
             newGameState.toastInfo.description += ` ${toDiscardCount} überzählige Karte(n) abgelegt.`;
        }
    } else if (payload.option === 'play_extra_color' && payload.cardId) {
        const cardToPlayOriginal = newGameState.playerHand.find(c => c.id === payload.cardId && c.type === CARD_TYPES.COLOR);
        if (cardToPlayOriginal) {
            const cardToPlay = {...cardToPlayOriginal};
            playCardFn(cardToPlay, 'Schlangenzone', 'end', true);
            newGameState.toastInfo = { title: "Risiko-Belohnung", description: `Zusätzliche Farbkarte ${cardToPlay.name} gespielt.`};
        } else {
            newGameState.toastInfo = { title: "Fehler", description: "Ausgewählte Farbkarte nicht gefunden oder ungültig.", variant: "destructive" };
        }
    } else if (payload.option === 'swap_card' && payload.handCardId && payload.snakeCardId && payload.snakeIndex !== undefined) {
        const handCardIndex = newGameState.playerHand.findIndex(c => c.id === payload.handCardId && c.type === CARD_TYPES.COLOR);
        const snakeCardIndex = newGameState.playerSnakes[payload.snakeIndex].findIndex(c => c.id === payload.snakeCardId && c.type === CARD_TYPES.COLOR);

        if (handCardIndex > -1 && snakeCardIndex > -1) {
            const cardFromHandOriginal = newGameState.playerHand[handCardIndex];
            const cardFromSnakeOriginal = newGameState.playerSnakes[payload.snakeIndex][snakeCardIndex];
            const cardFromHand = {...cardFromHandOriginal};
            const cardFromSnake = {...cardFromSnakeOriginal};

            newGameState.playerHand.splice(handCardIndex, 1, cardFromSnake);
            newGameState.playerSnakes[payload.snakeIndex].splice(snakeCardIndex, 1, cardFromHand);
            newGameState.toastInfo = { title: "Risiko-Belohnung", description: `Karten getauscht: ${cardFromHand.name} mit ${cardFromSnake.name}.`};
        } else {
            newGameState.toastInfo = { title: "Fehler", description: "Karten für Tausch nicht gefunden oder ungültig (nur Farbkarten).", variant: "destructive" };
        }
    } else if (payload.option === 'draw_from_discard') {
        if (newGameState.discardPile.length > 0) {
            const drawnCardOriginal = newGameState.discardPile.pop();
            const drawnCard = {...drawnCardOriginal};
            newGameState.playerHand.push(drawnCard);
            newGameState.toastInfo = { title: "Risiko-Belohnung", description: `Karte ${drawnCard.name} vom Ablagestapel gezogen.`};
            if (newGameState.playerHand.length > 5) {
                const toDiscardCount = newGameState.playerHand.length - 5;
                const cardsToDiscardOriginal = newGameState.playerHand.slice(0, toDiscardCount);
                const cardsToDiscard = cardsToDiscardOriginal.map(c => ({...c}));
                newGameState.playerHand = newGameState.playerHand.slice(toDiscardCount);
                newGameState.discardPile = [...newGameState.discardPile, ...cardsToDiscard];
                newGameState.toastInfo.description += ` ${toDiscardCount} überzählige Karte(n) abgelegt.`;
            }
        } else {
            newGameState.toastInfo = { title: "Fehler", description: "Ablagestapel ist leer.", variant: "destructive" };
        }
    }
    return newGameState;
};