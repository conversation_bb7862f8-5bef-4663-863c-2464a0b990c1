import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import Card from '@/components/Card';
import { ScrollArea } from '@/components/ui/scroll-area';

const InteractionModal = ({ isOpen, onClose, modalType, modalData, gameState, onSubmit }) => {
  const [selectedOption, setSelectedOption] = useState(null);
  const [selectedItems, setSelectedItems] = useState([]);

  useEffect(() => {
    setSelectedOption(null);
    setSelectedItems([]);
  }, [isOpen, modalType]);

  const handleItemSelect = (item, limit = 1) => {
    setSelectedItems(prev => {
      const موجود = prev.find(i => i.id === item.id);
      if (موجود) {
        return prev.filter(i => i.id !== item.id);
      } else {
        if (prev.length < limit) {
          return [...prev, item];
        }
        return [item];
      }
    });
  };

  const handleSubmit = () => {
    let payload = { option: selectedOption };
    if (modalType === 'SCHLANGENFRASS_OPTIONS' && selectedOption === 'a') {
        payload.selectedCardId = selectedItems[0]?.id;
        payload.snakeIndex = selectedItems[0]?.snakeIndex;
    } else if (modalType === 'FARBENDIEB_CHOOSE_CARD') {
        // First step: card selection
        payload.selectedCard = selectedItems[0];
        payload.step = 'card_selected';
    } else if (modalType === 'FARBENDIEB_CHOOSE_POSITION') {
        // Second step: position selection
        payload.targetSnakeIndex = selectedItems[0]?.snakeIndex;
        payload.targetPosition = selectedItems[0]?.position;
        payload.step = 'position_selected';
        payload.stolenCard = modalData.stolenCard;
        payload.sourceInfo = modalData.sourceInfo;
    } else if (modalType === 'SCHLANGENGRUBE_CHOOSE_OPPONENT') {
        payload.opponentIndex = selectedOption;
    } else if (modalType === 'FARBENFUSION_CHOOSE_CARDS') {
        payload.snakeIndex = modalData.snakeIndex;
        payload.card1Index = selectedItems[0]?.originalIndex;
        payload.card2Index = selectedItems[1]?.originalIndex;
    } else if (modalType === 'SCHLANGENHÄUTUNG_CHOOSE_SECTION') {
        payload.snakeIndex = modalData.snakeIndex;
        payload.startIndex = Math.min(...selectedItems.map(item => item.originalIndex));
        payload.endIndex = Math.max(...selectedItems.map(item => item.originalIndex));
        payload.newOrderArrayOfCards = selectedItems.sort((a,b) => a.originalIndex - b.originalIndex);
    } else if (modalType === 'SCHLANGENKORB_OPTIONS') {
        if (selectedOption === 'b') {
        } else if (selectedOption === 'c') {
        }
    }

    onSubmit(modalType, payload);
    onClose();
  };

  const renderContent = () => {
    switch (modalType) {
      case 'SCHLANGENFRASS_OPTIONS':
        return (
          <>
            <DialogDescription>Wähle eine Aktion für Schlangenfrass:</DialogDescription>
            <div className="space-y-2 my-4">
              {['a', 'b', 'c'].map(opt => (
                <Button key={opt} variant={selectedOption === opt ? "default" : "outline"} onClick={() => setSelectedOption(opt)} className="w-full">
                  {opt === 'a' && "Eigene Karte entfernen"}
                  {opt === 'b' && "Eigene Schlangenblockade entfernen"}
                  {opt === 'c' && "Zwei Karten von Mitspielern entfernen"}
                </Button>
              ))}
              {selectedOption === 'a' && (
                <ScrollArea className="h-40 border rounded p-2 mt-2">
                  <p className="text-sm mb-1">Wähle eine Karte aus deiner Schlange:</p>
                  {gameState.playerSnakes.map((snake, snakeIdx) =>
                    snake.map(card => (
                      <div key={card.id} onClick={() => handleItemSelect({...card, snakeIndex: snakeIdx})} className={`p-1 my-0.5 rounded cursor-pointer ${selectedItems[0]?.id === card.id ? 'bg-primary-gold/30' : 'hover:bg-slate-700'}`}>
                        <Card id={card.id} text={card.name} type={card.color || card.type} points={card.points} />
                      </div>
                    ))
                  ).flat()}
                </ScrollArea>
              )}
            </div>
          </>
        );
      case 'FARBENDIEB_CHOOSE_CARD':
         return (
            <>
                <DialogDescription>Wähle eine Karte aus einer beliebigen Schlange (auch deiner eigenen), um sie zu stehlen/verschieben.</DialogDescription>
                <ScrollArea className="h-60 border rounded p-2 my-4">
                  {/* Player's own snakes */}
                  {gameState.playerSnakes.map((snake, snakeIdx) =>
                    snake.map((card, cardIdx) => (
                      <div key={`player-snake-${snakeIdx}-card-${card.id}`}
                           onClick={() => handleItemSelect({
                             id: card.id,
                             isPlayerCard: true,
                             snakeIndex: snakeIdx,
                             cardIndex: cardIdx,
                             sourceType: 'player'
                           })}
                           className={`p-1 my-0.5 rounded cursor-pointer ${selectedItems[0]?.id === card.id ? 'bg-primary-gold/30' : 'hover:bg-slate-700'}`}>
                        <p className="text-xs text-blue-400">Deine Schlange {snakeIdx + 1}</p>
                        <Card id={card.id} text={card.name} type={card.color || card.type} points={card.points} />
                      </div>
                    ))
                  ).flat()}

                  {/* Opponent snakes */}
                  {gameState.aiOpponentsData.map((opponent, oppIdx) => (
                    opponent.snakes.map((snake, snakeIdx) => (
                        snake.map((card, cardIdx) => (
                            <div key={`opp-${oppIdx}-snake-${snakeIdx}-card-${card.id}`}
                                 onClick={() => handleItemSelect({
                                   id: card.id,
                                   opponentIndex: oppIdx,
                                   snakeIndex: snakeIdx,
                                   cardIndex: cardIdx,
                                   sourceType: 'opponent'
                                 })}
                                 className={`p-1 my-0.5 rounded cursor-pointer ${selectedItems[0]?.id === card.id ? 'bg-primary-gold/30' : 'hover:bg-slate-700'}`}>
                                <p className="text-xs text-red-400">Gegner {oppIdx + 1}, Schlange {snakeIdx + 1}</p>
                                <Card id={card.id} text={card.name} type={card.color || card.type} points={card.points} />
                            </div>
                        ))
                    )).flat()
                  )).flat()}
                </ScrollArea>
            </>
         );
      case 'FARBENDIEB_CHOOSE_POSITION':
         return (
            <>
                <DialogDescription>
                  Wähle die Position in deiner Schlange, wo die gestohlene Karte "{modalData.stolenCard?.name}" eingefügt werden soll.
                </DialogDescription>
                <ScrollArea className="h-40 border rounded p-2 my-4">
                  {gameState.playerSnakes.map((snake, snakeIdx) => (
                    <div key={`snake-${snakeIdx}`} className="mb-4">
                      <p className="text-sm mb-2">Schlange {snakeIdx + 1}:</p>
                      <div className="flex flex-wrap gap-1">
                        {/* Position at start */}
                        <div
                          onClick={() => handleItemSelect({ snakeIndex: snakeIdx, position: 0 })}
                          className={`p-2 border-2 border-dashed border-primary-gold/50 rounded cursor-pointer hover:bg-primary-gold/20 ${selectedItems[0]?.snakeIndex === snakeIdx && selectedItems[0]?.position === 0 ? 'bg-primary-gold/30' : ''}`}
                        >
                          <span className="text-xs">Anfang</span>
                        </div>

                        {/* Positions between cards */}
                        {snake.map((card, cardIdx) => (
                          <React.Fragment key={card.id}>
                            <div className="p-1 bg-slate-600 rounded">
                              <Card id={card.id} text={card.name} type={card.color || card.type} points={card.points} />
                            </div>
                            <div
                              onClick={() => handleItemSelect({ snakeIndex: snakeIdx, position: cardIdx + 1 })}
                              className={`p-2 border-2 border-dashed border-primary-gold/50 rounded cursor-pointer hover:bg-primary-gold/20 ${selectedItems[0]?.snakeIndex === snakeIdx && selectedItems[0]?.position === cardIdx + 1 ? 'bg-primary-gold/30' : ''}`}
                            >
                              <span className="text-xs">Nach {cardIdx + 1}</span>
                            </div>
                          </React.Fragment>
                        ))}

                        {/* If snake is empty, show single position */}
                        {snake.length === 0 && (
                          <div
                            onClick={() => handleItemSelect({ snakeIndex: snakeIdx, position: 0 })}
                            className={`p-2 border-2 border-dashed border-primary-gold/50 rounded cursor-pointer hover:bg-primary-gold/20 ${selectedItems[0]?.snakeIndex === snakeIdx && selectedItems[0]?.position === 0 ? 'bg-primary-gold/30' : ''}`}
                          >
                            <span className="text-xs">Erste Position</span>
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </ScrollArea>
            </>
         );
      case 'SCHLANGENGRUBE_CHOOSE_OPPONENT':
        return (
            <>
                <DialogDescription>Wähle einen Gegner, der eine Runde aussetzen soll.</DialogDescription>
                <div className="space-y-2 my-4">
                {gameState.aiOpponentsData.map((opponent, idx) => (
                    <Button key={`opp-grube-${idx}`} variant={selectedOption === idx ? "default" : "outline"} onClick={() => setSelectedOption(idx)} className="w-full">
                        Gegner {idx + 1}
                    </Button>
                ))}
                </div>
            </>
        );
      case 'FARBENFUSION_CHOOSE_CARDS':
        return (
            <>
                <DialogDescription>Wähle zwei benachbarte Karten gleicher Farbe aus deiner aktiven Schlange ({modalData.snakeIndex + 1}) zur Fusion.</DialogDescription>
                <ScrollArea className="h-40 border rounded p-2 my-4">
                    {modalData.snake.map((card, index) => (
                        <div key={card.id} onClick={() => handleItemSelect({...card, originalIndex: index}, 2)} className={`p-1 my-0.5 rounded cursor-pointer ${selectedItems.find(i=>i.id===card.id) ? 'bg-primary-gold/30' : 'hover:bg-slate-700'}`}>
                            <Card id={card.id} text={card.name} type={card.color || card.type} points={card.points} />
                        </div>
                    ))}
                </ScrollArea>
            </>
        );
    case 'SCHLANGENHÄUTUNG_CHOOSE_SECTION':
        return (
            <>
                <DialogDescription>Wähle 3-5 Karten aus deiner aktiven Schlange ({modalData.snakeIndex + 1}) zur Neuanordnung.</DialogDescription>
                 <ScrollArea className="h-40 border rounded p-2 my-4">
                    {modalData.snake.map((card, index) => (
                        <div key={card.id} onClick={() => handleItemSelect({...card, originalIndex: index}, 5)} className={`p-1 my-0.5 rounded cursor-pointer ${selectedItems.find(i=>i.id===card.id) ? 'bg-primary-gold/30' : 'hover:bg-slate-700'}`}>
                            <Card id={card.id} text={card.name} type={card.color || card.type} points={card.points} />
                        </div>
                    ))}
                </ScrollArea>
                {selectedItems.length >=3 && selectedItems.length <= 5 ? null : <p className="text-xs text-red-400">Bitte wähle 3 bis 5 Karten.</p>}
            </>
        );
    case 'SCHLANGENKORB_OPTIONS':
        const korbOptions = [
            { id: 'a', label: "Ziehe 3 Karten, spiele bis zu 2" },
            { id: 'b', label: "Tausche bis zu 2 Karten (Hand/Schlange)" },
            { id: 'c', label: "Entferne Blockade ODER gegn. Sonderkarte" },
            { id: 'd', label: "Zwei zusätzliche Aktionen" },
        ];
        return (
            <>
                <DialogDescription>Wähle einen Bonus des Schlangenkorbs:</DialogDescription>
                <div className="space-y-2 my-4">
                {korbOptions.map(opt => (
                    <Button key={opt.id} variant={selectedOption === opt.id ? "default" : "outline"} onClick={() => setSelectedOption(opt.id)} className="w-full">
                        {opt.label}
                    </Button>
                ))}
                </div>
            </>
        );

      default:
        return <DialogDescription>Interaktion nicht vollständig konfiguriert.</DialogDescription>;
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="bg-slate-800 text-slate-100 border-primary-gold">
        <DialogHeader>
          <DialogTitle className="text-primary-gold font-heading">{modalData?.cardName || modalType.replace(/_/g, ' ')}</DialogTitle>
        </DialogHeader>
        {renderContent()}
        <DialogFooter>
          <Button variant="outline" onClick={onClose} className="text-slate-300 border-slate-500 hover:bg-slate-700">Abbrechen</Button>
          <Button onClick={handleSubmit} disabled={modalType === 'SCHLANGENGRUBE_CHOOSE_OPPONENT' ? (gameState?.aiOpponentsData?.length > 1 && selectedOption === null) : (!selectedOption && !selectedItems.length)} className="bg-primary-gold hover:bg-primary-gold/80 text-slate-900">Bestätigen</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default InteractionModal;