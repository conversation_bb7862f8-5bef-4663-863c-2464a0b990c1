import { supabase } from '@/lib/supabaseClient';
import { CARD_TYPES, SPECIAL_CARDS_NAMES } from '@/utils/gameRules';
import { debugLog, debugWarn, debugError } from '@/utils/debugUtils.jsx';

// Debug-Flag für erzwungene Fallback-Logik über localStorage für Persistenz
const getForceFallback = () => {
  const stored = localStorage.getItem('debug_force_fallback');
  return stored === 'true';
};

const setForceFallbackPersistent = (value) => {
  if (value) {
    localStorage.setItem('debug_force_fallback', 'true');
    localStorage.setItem('debug_force_fallback_timestamp', Date.now().toString());
  } else {
    localStorage.removeItem('debug_force_fallback');
    localStorage.removeItem('debug_force_fallback_timestamp');
  }
  debugLog(`[AI Logic] 🔧 Force fallback mode: ${value ? 'ENABLED' : 'DISABLED'} (persistent)`);
};

// Auto-Cleanup nach 30 Sekunden
const checkForceFallbackExpiry = () => {
  const timestamp = localStorage.getItem('debug_force_fallback_timestamp');
  if (timestamp) {
    const elapsed = Date.now() - parseInt(timestamp);
    if (elapsed > 30000) { // 30 Sekunden
      setForceFallbackPersistent(false);
      debugLog('[AI Logic] 🔧 Force fallback mode expired after 30s');
    }
  }
};

export const setForceFallback = (value) => {
  setForceFallbackPersistent(value);
  if (value) {
    setTimeout(() => {
      setForceFallbackPersistent(false);
      debugLog('[AI Logic] 🔧 Force fallback mode auto-disabled after timeout');
    }, 30000);
  }
};

// Helper function to find the best target for Farbendieb card
const findBestFarbendiebTarget = (gameState, aiPlayerId) => {
  let bestTarget = null;
  let bestScore = -1;

  // Check opponent snakes for high-value cards
  gameState.aiOpponentsData.forEach((opponent, oppIdx) => {
    if (opponent.id !== aiPlayerId) { // Don't steal from self
      opponent.snakes.forEach((snake, snakeIdx) => {
        snake.forEach((card, cardIdx) => {
          // Score based on card points and strategic value
          let score = card.points || 0;

          // Bonus for special cards
          if (card.type === CARD_TYPES.SPECIAL) {
            score += 5;
          }

          // Bonus for cards that would complete sequences in AI's snake
          // (This is a simplified heuristic)
          score += 2;

          if (score > bestScore) {
            bestScore = score;
            bestTarget = {
              card,
              sourceType: 'opponent',
              opponentIndex: oppIdx,
              snakeIndex: snakeIdx,
              cardIndex: cardIdx,
              score
            };
          }
        });
      });
    }
  });

  // Check human player snakes
  gameState.playerSnakes.forEach((snake, snakeIdx) => {
    snake.forEach((card, cardIdx) => {
      let score = card.points || 0;

      if (card.type === CARD_TYPES.SPECIAL) {
        score += 5;
      }

      score += 2;

      if (score > bestScore) {
        bestScore = score;
        bestTarget = {
          card,
          sourceType: 'player',
          snakeIndex: snakeIdx,
          cardIndex: cardIdx,
          score
        };
      }
    });
  });

  // Also consider repositioning own cards for better sequences
  const aiPlayer = gameState.aiOpponentsData.find(opp => opp.id === aiPlayerId);
  if (aiPlayer) {
    aiPlayer.snakes.forEach((snake, snakeIdx) => {
      snake.forEach((card, cardIdx) => {
        // Lower score for own cards (only reposition if really beneficial)
        let score = (card.points || 0) * 0.3;

        // Check if repositioning would create better sequences
        // (This is a simplified check)
        if (cardIdx > 0 && cardIdx < snake.length - 1) {
          score += 1; // Slight bonus for middle cards that might be repositioned
        }

        if (score > bestScore && score > 3) { // Only consider if score is decent
          bestScore = score;
          bestTarget = {
            card,
            sourceType: 'self',
            snakeIndex: snakeIdx,
            cardIndex: cardIdx,
            score
          };
        }
      });
    });
  }

  return bestTarget;
};

const fallbackMakeBasicMove = (aiPlayerData, gameState) => {
  debugWarn(`[AI Logic Fallback][P${aiPlayerData.id}] Using fallback logic due to ${getForceFallback() ? 'FORCED DEBUG MODE' : 'API error or no move'}.`);

  let cardToPlay = null;
  let targetSnakeIndex = aiPlayerData.activeSnakeIndex || 0;
  let position = null;

  const colorCardsPlayed = aiPlayerData.colorCardsPlayedThisTurn || 0;
  const specialCardsPlayed = aiPlayerData.specialCardsPlayedThisTurn || 0;
  const isVerdopplerActive = aiPlayerData.verdopplerActive || gameState.playedSpecialCardsHistory.some(
    c => c.name === SPECIAL_CARDS_NAMES.DOUBLER && c.turnPlayed === gameState.turnNumber && c.playerId === aiPlayerData.id
  );
  const maxColor = isVerdopplerActive ? 3 : 1;
  const maxSpecial = isVerdopplerActive ? 3 : 1;

  // DEBUG CARD DETECTION: Priorisiere Debug-Karten für Tests
  const debugCards = aiPlayerData.hand.filter(c => c.id && c.id.startsWith('debug-'));
  const hasDebugCard = debugCards.length > 0;
  const debugCardToTest = debugCards[0]; // Spiele erste Debug-Karte

  // NEUE VERDOPPLER-STRATEGIE: Spiele Verdoppler zuerst, wenn verfügbar und noch kein Special gespielt
  const hasVerdoppler = aiPlayerData.hand.find(c => c.name === SPECIAL_CARDS_NAMES.DOUBLER);
  const shouldPlayVerdopplerFirst = hasVerdoppler && !isVerdopplerActive && specialCardsPlayed === 0;

  // AGGRESSIV: Wenn Verdoppler aktiv ist, versuche mehr Karten zu spielen
  const isVerdopplerJustActivated = isVerdopplerActive && (colorCardsPlayed + specialCardsPlayed) < 3;

  // SPECIAL CARD TESTING PRIORITY: Debug-Karten haben höchste Priorität
  const shouldPlayDebugCardFirst = hasDebugCard && specialCardsPlayed === 0;

  debugLog(`[AI Logic Fallback][P${aiPlayerData.id}] Analysis: colorPlayed=${colorCardsPlayed}/${maxColor}, specialPlayed=${specialCardsPlayed}/${maxSpecial}, hasVerdoppler=${!!hasVerdoppler}, shouldPlayVerdopplerFirst=${shouldPlayVerdopplerFirst}, verdopplerJustActivated=${isVerdopplerJustActivated}`);
  debugLog(`[AI Logic Fallback][P${aiPlayerData.id}] 🎴 DEBUG CARDS: hasDebugCard=${hasDebugCard}, debugCardToTest=${debugCardToTest?.name}, shouldPlayDebugCardFirst=${shouldPlayDebugCardFirst}`);

  if (aiPlayerData.isBlockedByGrube) {
    if (specialCardsPlayed < maxSpecial) {
      // Wenn blockiert, spiele eine defensive Sonderkarte
      cardToPlay = aiPlayerData.hand.find(c =>
        c.type === CARD_TYPES.SPECIAL &&
        (c.name === SPECIAL_CARDS_NAMES.COLOR_PROTECTION || c.name === SPECIAL_CARDS_NAMES.SNAKE_BLOCKADE)
      ) || aiPlayerData.hand.find(c => c.type === CARD_TYPES.SPECIAL);
      debugLog(`[AI Logic Fallback][P${aiPlayerData.id}] Blocked by Grube, choosing defensive special: ${cardToPlay?.name}`);
    }
  } else {
    // PRIORISIERUNG: 1. Debug-Karten (für Tests), 2. Verdoppler, 3. Farbkarten, 4. andere Specials
    if (shouldPlayDebugCardFirst) {
      cardToPlay = debugCardToTest;
      position = null; // Sonderkarten brauchen meist keine Position

      // SPEZIELLE BEHANDLUNG für verschiedene Sonderkarten
      if (cardToPlay.name === SPECIAL_CARDS_NAMES.SNAKE_PIT) {
        // Schlangengrube: Wähle ersten verfügbaren Gegner (wird automatisch vom System behandelt)
        debugLog(`[AI Logic Fallback][P${aiPlayerData.id}] 🕳️ SCHLANGENGRUBE: Will target first available opponent`);
      } else if (cardToPlay.name === SPECIAL_CARDS_NAMES.COLOR_THIEF) {
        // Farbendieb: Intelligente Auswahl der besten Karte zum Stehlen
        const bestTheftTarget = findBestFarbendiebTarget(gameState, aiPlayerData.id);
        if (bestTheftTarget) {
          debugLog(`[AI Logic Fallback][P${aiPlayerData.id}] 🎭 FARBENDIEB: Will steal ${bestTheftTarget.card.name} from ${bestTheftTarget.sourceType === 'opponent' ? `opponent ${bestTheftTarget.opponentIndex + 1}` : 'own snake'}`);
          // The AI will use the automatic theft logic implemented in the card effect
        } else {
          debugLog(`[AI Logic Fallback][P${aiPlayerData.id}] 🎭 FARBENDIEB: No good theft targets found`);
        }
      }

      debugLog(`[AI Logic Fallback][P${aiPlayerData.id}] 🎴 PRIORITIZING DEBUG CARD for testing: ${cardToPlay.name} (${cardToPlay.id})`);
    } else if (shouldPlayVerdopplerFirst) {
      cardToPlay = hasVerdoppler;
      position = null; // Verdoppler braucht keine Position
      debugLog(`[AI Logic Fallback][P${aiPlayerData.id}] 🎯 PRIORITIZING VERDOPPLER for maximum actions: ${cardToPlay.name} (${cardToPlay.id})`);
    } else if (colorCardsPlayed < maxColor || isVerdopplerJustActivated) {
      // Priorisiere Farbkarten mit höheren Punkten
      cardToPlay = aiPlayerData.hand.find(c => c.type === CARD_TYPES.COLOR && c.points > 1) ||
                  aiPlayerData.hand.find(c => c.type === CARD_TYPES.COLOR);
      if (cardToPlay) {
        position = 'end';
        if (aiPlayerData.snakes.length === 0 || !aiPlayerData.snakes[targetSnakeIndex] || aiPlayerData.snakes[targetSnakeIndex].length === 0) {
          targetSnakeIndex = 0;
        } else if (aiPlayerData.snakes.length < 2 && aiPlayerData.snakes[0].length > 2) {
          targetSnakeIndex = 1;
        }
        debugLog(`[AI Logic Fallback][P${aiPlayerData.id}] Choosing color card: ${cardToPlay.name} (${cardToPlay.points} pts) on snake ${targetSnakeIndex}`);
      }
    }
    if (!cardToPlay && (specialCardsPlayed < maxSpecial || isVerdopplerJustActivated)) {
      // Wenn keine Farbkarte gespielt wurde oder Verdoppler aktiv, spiele eine strategische Sonderkarte
      cardToPlay = aiPlayerData.hand.find(c =>
        c.type === CARD_TYPES.SPECIAL &&
        (c.name === SPECIAL_CARDS_NAMES.RAINBOW_SNAKE || c.name === SPECIAL_CARDS_NAMES.COLOR_FUSION)
      ) || aiPlayerData.hand.find(c => c.type === CARD_TYPES.SPECIAL);
      position = null;
      debugLog(`[AI Logic Fallback][P${aiPlayerData.id}] Choosing other special card: ${cardToPlay?.name} (verdopplerActive=${isVerdopplerActive})`);
    }
  }

  if (!cardToPlay && aiPlayerData.hand.length > 0 && (colorCardsPlayed + specialCardsPlayed === 0)) {
    cardToPlay = aiPlayerData.hand[0];
    if (cardToPlay.type === CARD_TYPES.COLOR && !aiPlayerData.isBlockedByGrube) {
        position = 'end';
        targetSnakeIndex = 0;
    } else if (cardToPlay.type === CARD_TYPES.SPECIAL) {
        position = null;
        targetSnakeIndex = 0;
    } else {
        cardToPlay = null;
    }
    debugLog(`[AI Logic Fallback][P${aiPlayerData.id}] Last resort: playing first card ${cardToPlay?.name}`);
  }

  if (cardToPlay) {
    // KRITISCH: Sicherstellen, dass der Kartentyp korrekt ist
    const correctedCardToPlay = {
      ...cardToPlay,
      type: cardToPlay.name === SPECIAL_CARDS_NAMES.DOUBLER ? CARD_TYPES.SPECIAL : cardToPlay.type
    };
    debugLog(`[AI Logic Fallback][P${aiPlayerData.id}] ✅ Final choice: ${correctedCardToPlay.name} (${correctedCardToPlay.type})`);
    const reasoning = shouldPlayDebugCardFirst ? 'Debug card priority for testing' :
                     shouldPlayVerdopplerFirst ? 'Verdoppler priority' : 'basic move';
    return { cardToPlay: correctedCardToPlay, targetSnakeIndex, position, reasoning: `Fallback: ${reasoning}.` };
  }

  debugWarn(`[AI Logic Fallback][P${aiPlayerData.id}] ❌ No move possible!`);
  return { cardToPlay: null, reasoning: "Fallback: No basic move possible." };
};

export const makeAiMove = async (aiPlayerData, gameState) => {
  debugLog(`[AI Logic][P${aiPlayerData.id}] Starting move request. Hand:`, aiPlayerData.hand.map(c=>c.name));

  // Prüfe Ablaufzeit für Force-Fallback-Modus
  checkForceFallbackExpiry();

  debugLog(`[AI Logic][P${aiPlayerData.id}] 🔧 Current forceUseFallback status: ${getForceFallback()}`);

  // In Debug-Modus: Erzwinge Fallback-Logik wenn aktiviert
  if (getForceFallback()) {
    debugLog(`[AI Logic][P${aiPlayerData.id}] 🔄 DEBUG MODE: Forcing fallback logic instead of AI API`);
    return fallbackMakeBasicMove(aiPlayerData, gameState);
  }

  const gameConfig = JSON.parse(localStorage.getItem('gameConfig') || '{}');
  const selectedOpenAiModel = gameConfig.selectedOpenAiModel || "mistralai/mistral-7b-instruct:free";

  const isVerdopplerActiveForAi = gameState.playedSpecialCardsHistory.some(
    c => c.name === SPECIAL_CARDS_NAMES.DOUBLER && c.turnPlayed === gameState.turnNumber && c.playerId === aiPlayerData.id
  );

  const simplifiedCard = (card) => ({
    id: card.id,
    name: card.name,
    type: card.type,
    color: card.color,
    points: card.points
  });

  const gameStateForAI = {
    aiPlayer: {
        id: aiPlayerData.id,
        name: aiPlayerData.name,
        hand: aiPlayerData.hand.map(simplifiedCard),
        snakes: aiPlayerData.snakes.map(s => s.map(simplifiedCard)),
        activeSnakeIndex: aiPlayerData.activeSnakeIndex || 0,
        points: aiPlayerData.points,
        colorCardsPlayedThisTurn: aiPlayerData.colorCardsPlayedThisTurn || 0,
        specialCardsPlayedThisTurn: aiPlayerData.specialCardsPlayedThisTurn || 0,
        secretTask: aiPlayerData.secretTask ? {
            id: aiPlayerData.secretTask.id,
            type: aiPlayerData.secretTask.type,
            description: aiPlayerData.secretTask.description,
            points: aiPlayerData.secretTask.points,
            isCompleted: aiPlayerData.secretTask.isCompleted,
            targetColor: aiPlayerData.secretTask.targetColor
        } : undefined,
        isBlockedByGrube: aiPlayerData.isBlockedByGrube || false,
        mustPlaySpecialCard: aiPlayerData.mustPlaySpecialCard || false,
    },
    openTasks: gameState.openTasks.filter(t => !t.isCompleted).map(t => ({
        id: t.id,
        type: t.type,
        description: t.description,
        points: t.points,
        isCompleted: t.isCompleted,
        targetColor: t.targetColor,
    })),
    humanPlayerSnakes: gameState.playerSnakes.map(s => s.map(simplifiedCard)),
    otherAiPlayersData: gameState.aiOpponentsData
        .filter(opp => opp.id !== aiPlayerData.id)
        .map(opp => ({
            id: opp.id,
            name: opp.name,
            snakes: opp.snakes.map(s => s.map(simplifiedCard)),
            activeSnakeIndex: opp.activeSnakeIndex,
            points: opp.points,
            colorCardsPlayedThisTurn: opp.colorCardsPlayedThisTurn || 0,
            specialCardsPlayedThisTurn: opp.specialCardsPlayedThisTurn || 0,
            isBlockedByGrube: opp.isBlockedByGrube || false,
        })),
    deckSize: gameState.deck.length,
    discardPileSize: gameState.discardPile.length,
    turnNumber: gameState.turnNumber,
    isVerdopplerActiveForAi: isVerdopplerActiveForAi,
    gamePhase: gameState.gamePhase,
    selectedModel: selectedOpenAiModel,
  };

  try {
    debugLog(`[AI Logic][P${aiPlayerData.id}] 🌐 Calling AI API (fallback mode: ${getForceFallback() ? 'FORCED' : 'disabled'})...`);
    const { data: moveData, error } = await supabase.functions.invoke('get-ai-move', {
      body: gameStateForAI,
    });

    if (error) {
      debugError(`[AI Logic][P${aiPlayerData.id}] ❌ AI API Error:`, error);
      debugLog(`[AI Logic][P${aiPlayerData.id}] 🔄 Falling back to local logic due to API error`);
      return fallbackMakeBasicMove(aiPlayerData, gameState);
    }

    debugLog(`[AI Logic][P${aiPlayerData.id}] 🌐 AI API Response:`, moveData);

    if (!moveData || moveData.actionType === "error") {
        debugError(`[AI Logic][P${aiPlayerData.id}] ❌ AI returned error:`, moveData?.error, "Reasoning:", moveData?.reasoning);
        debugLog(`[AI Logic][P${aiPlayerData.id}] 🔄 Falling back to local logic due to AI error`);
        return fallbackMakeBasicMove(aiPlayerData, gameState);
    }

    if (moveData.actionType === "pass") {
        debugLog(`[AI Logic][P${aiPlayerData.id}] 🚫 AI chose to pass. Reasoning: ${moveData.reasoning || 'N/A'}`);
        return { cardToPlay: null, reasoning: moveData.reasoning };
    }

    if (moveData.actionType === "playCard" && moveData.cardToPlay && moveData.cardToPlay.id) {
        const cardFromHand = aiPlayerData.hand.find(c => c.id === moveData.cardToPlay.id);
        if (!cardFromHand) {
            debugError(`[AI Logic][P${aiPlayerData.id}] ❌ AI chose invalid card ${moveData.cardToPlay.id} (${moveData.cardToPlay.name})! Hand:`, aiPlayerData.hand.map(c=> ({id: c.id, name: c.name})));
            debugLog(`[AI Logic][P${aiPlayerData.id}] 🔄 Falling back to local logic due to invalid card choice`);
             return fallbackMakeBasicMove(aiPlayerData, gameState);
        }
      debugLog(`[AI Logic][P${aiPlayerData.id}] ✅ AI chose: ${cardFromHand.name} on snake ${moveData.targetSnakeIndex} at ${moveData.position}. Reasoning: ${moveData.reasoning || 'N/A'}`);
      return {
        cardToPlay: cardFromHand,
        targetSnakeIndex: moveData.targetSnakeIndex,
        position: moveData.position,
        reasoning: moveData.reasoning
      };
    }

    debugWarn(`[AI Logic][P${aiPlayerData.id}] ⚠️ Invalid AI response:`, moveData);
    debugLog(`[AI Logic][P${aiPlayerData.id}] 🔄 Falling back to local logic due to invalid response`);
    return fallbackMakeBasicMove(aiPlayerData, gameState);

  } catch (e) {
    debugError(`[AI Logic][P${aiPlayerData.id}] ❌ Critical AI API error:`, e);
    debugLog(`[AI Logic][P${aiPlayerData.id}] 🔄 Falling back to local logic due to critical error`);
    return fallbackMakeBasicMove(aiPlayerData, gameState);
  }
};
